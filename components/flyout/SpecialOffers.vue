<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel code="special_offers" />
		</template>
		<template #content>
			<div class="special-offers" v-if="content?.length" v-interpolation>
				<div class="special-offer" v-for="item in content" :key="item.code">
					<div class="image" v-if="item.badge_image_upload_path">
						<BaseUiImage loading="lazy" :src="item.badge_image_upload_path" default="/images/no-image-50.webp" width="50" height="50" :alt="item.title" />
					</div>
					<div class="cnt" :class="{'no-image': !item.badge_image_upload_path}">
						<div class="title">{{ item.title }}</div>
						<BaseUiToggleContent v-if="item.short_description" :content="item.short_description" :limit="400" v-slot="{content, contentLength, active, onToggle}">
							{{contentLength}}
							<div class="content" v-html="content" />
							<span class="toggle" v-if="contentLength > 400" @click="onToggle">{{ active ? labels.get('show_less') : labels.get('show_more')}}</span>
						</BaseUiToggleContent>
					</div>
				</div>
			</div>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const modal = useModal()
	const labels = useLabels();
	const content = computed(() => modal.get('flyout')?.content || null);
</script>

<style scoped lang="less">
	.image{flex: 0 0 50px;}
	.special-offer{display: flex; border-bottom: 1px solid var(--gray3); padding: 25px var(--flyoutSideOffset); gap: 20px; font-size: 15px;}
	.title{font-size: 18px; font-weight: bold;}
	.cnt{
		padding-top: 13px;
		&.no-image{padding: 0;}
	}
	.content{
		padding-top: 15px;
		:deep(p:last-child){padding-bottom: 0;}
	}
	.toggle{cursor: pointer; text-decoration: underline; color: var(--blueDark); font-size: 13px; display: inline-block; margin-top: 5px;}
</style>