<template>
	<article class="cp" :class="{'xxx': item.xxx}">
		<div class="cp-image-container">
			<!-- image -->
			<figure class="cp-image">
				<NuxtLink :to="item.url_without_domain" class="cp-image-inner">
					<BaseUiImage loading="lazy" :data="item.main_image_upload_path_thumb" default="/images/no-image.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />
					<!--<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width400-height400']" default="/images/no-image.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : item.title" />-->
				</NuxtLink>
			</figure>

			<ClientOnly>
				<div class="cp-toolbar">
					<CatalogSetWishlists :item="item" mode="grid" />
					<CatalogSetCompare :item="item" mode="grid" />
				</div>

				<!-- badges -->
				<CatalogProductBadges :item="item" :types="['image']" class="cp-badges-special" />
				

				<CatalogProductBadges :item="item" :energy="true" class="cp-badges" />
			</ClientOnly>
		</div>

		<div class="cp-cnt">
			<!-- main info -->
			<div class="cp-cnt-top">
				<div class="cp-title">
					<NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
				</div>

				<ClientOnly>
					<CatalogRates :item="item" />
					<CatalogProductInfo :item="item" />
				</ClientOnly>
			</div>

			<ClientOnly>
				<CatalogProductPrice :item="item" />
				<div v-if="installmentPrice" class="cp-price-installment" v-html="labels.get('installments_price_text').replace('%PRICE%', formatCurrency(installmentPrice))"></div>
				<!-- FIXME badge dodatnih 20% uz kod -->
			</ClientOnly>
		</div>

		<!-- age verification -->
		<CatalogAgeVerificationOverlay :item="props.item" />
	</article>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
	const {formatCurrency} = useCurrency();
	const {installmentPrice, imageBadges} = useProductData(props.item);
</script>

<style lang="less" scoped>
	.cp{
		display: flex; flex-direction: column; background: var(--white); border: 1px solid var(--white); border-radius: var(--borderRadius); position: relative; .transition(border-color);
		@media (min-width: @t){
			&:hover{border-color: var(--blueDark);}
		}
	}
	.cp-toolbar{position: absolute; top: 10px; right: 10px; z-index: 1; display: flex; flex-direction: column; gap: 5px;}
	.cp-image-container{padding: 20px 14px 16px; border-radius: var(--borderRadius) var(--borderRadius) 0 0; overflow: hidden; position: relative;}
	.cp-image{
		display: flex; align-items: center; justify-content: center; width: 100%; position: relative;
		a{display: flex; align-items: center; justify-content: center; height: 200px; z-index: 1; width: 100%;}
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	.cp-cnt{display: flex; flex-direction: column; flex-grow: 1; padding: 0 15px 15px; position: relative;}
	.cp-cnt-top{flex-grow: 1;}
	.cp-title{
		display: -webkit-box; text-overflow: ellipsis; -webkit-line-clamp: 3; -webkit-box-orient: vertical; overflow: hidden; font-size: 14px; line-height: 1.3; color: var(--black); margin-bottom: 13px;
		a, span {color: var(--black); text-decoration: none; cursor: pointer;}

		@media (max-width: @m){-webkit-line-clamp: 2; font-size: 12px;}
	}
	.cp-price-installment{
		display: block; margin-top: 1px; font-size: 12px; color: var(--black);
		:deep(strong){font-weight: normal;}
		:deep(p){padding-bottom: 0;}	
	}
	.cp-badges-special{position: absolute; top: 14px; left: 16px; z-index: 1; width: 40px;}
</style>
