<template>
	<BaseCatalogDetail log v-slot="{item}">
		<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" class="cd-bc wrapper" :remove-last="true" :links="true" />
		<div class="cd-main wrapper">
			<div class="cd-col1">
				<!-- images -->
				<CatalogProductImages :item="item" />

				<!-- attributes -->
				<BaseUiAccordion v-if="displayedAttributes?.length || item?.content || item?.element_content_state">
					<BaseUiAccordionPanel id="state" active v-if="item?.element_content_state">
						<!-- product state -->
						<template #header><BaseCmsLabel code='tab_product_state_description' /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-content-desc" v-interpolation v-html="item.element_content_state" />
						</template>
					</BaseUiAccordionPanel>
					<BaseUiAccordionPanel id="specs" active>
						<template #header><BaseCmsLabel code="tab_specs" /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-attributes">
								<div v-for="(item, key) in displayedAttributes" :key="key" class="cd-tab-attribute">
									<div class="cd-tab-attribute-title">{{ item.title }}</div>
									<div class="cd-tab-attribute-desc">
										{{ item.values.join(', ') }}
										<template v-if="item.unit"> {{ item.unit }}</template>
									</div>
								</div>
							</div>
							<div v-if="Object.keys(itemsAttributes).length > 6" class="cd-tab-more cd-tab-more-attr" @click="showAttr = !showAttr">{{ showAttr ? labels.get('show_less') : labels.get('show_more') }}</div>
						</template>
					</BaseUiAccordionPanel>	
					<BaseUiAccordionPanel id="desc" active v-if="item?.content">
						<!-- description -->
						<template #header><BaseCmsLabel code='tab_product_description' /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-content-desc">
								<BaseUiToggleContent :content="item.content" :limit="1000" v-slot="{content, contentLength, active, onToggle}">
									<div v-html="content" v-interpolation />
									<div v-if="contentLength > 1000" class="cd-tab-more cd-tab-more-attr" @click="onToggle">{{ active ? labels.get('show_less') : labels.get('show_more')}}</div>
								</BaseUiToggleContent>
							</div>
						</template>
					</BaseUiAccordionPanel>
				</BaseUiAccordion>

				<!-- FIXME povezani proizvodi -->

				<!-- manufacturer description -->
				<BaseUiAccordion class="cd-tab cd-tab-desc" v-if="item?.check_lists?.webcatalog_157271 || item?.check_lists?.webcatalog_201949 || item?.check_lists?.webcatalog_157270">
					<BaseUiAccordionPanel id="state" active>
						<!-- product state -->
						<template #header><BaseCmsLabel code="tab_product_manufacturer_desc" /></template>
						<template #body>
							<div class="cd-tab-content cd-tab-syndicate" :class="{'active': toggleSyndicate}">
								<CatalogSyndicateContent :item="item" />
							</div>
							<div class="cd-tab-more cd-tab-more-attr" @click="toggleSyndicate = !toggleSyndicate">{{ toggleSyndicate ? labels.get('show_less') : labels.get('show_more')}}</div>
						</template>
					</BaseUiAccordionPanel>
				</BaseUiAccordion>

				<!-- comments -->
				<div v-if="item.feedback_comment_widget" class="cd-tab cd-tab-comment" ref="comments">
					<BaseCmsLabel code='tab_comments' tag="div" class="cd-tab-title" :class="{'active': toggleCommentEl}" @click="toggleCommentContainer" />
					<div v-if="toggleCommentEl" class="cd-tab-content cd-tab-content-comment">
						<ClientOnly>
							<div class="cd-comments-info">
								<BaseFeedbackRatesWidget :data="item.feedback_rate_widget" v-slot="{rate, stars}">
									<div class="cd-comments-info-col1">
										<div v-if="rate > 0" class="cd-comments-rate-average">{{ rate }} od 5</div>
										<div class="cd-rate-section">
											<span class="cd-rate" v-html="stars" />
											<div class="cd-rate-votes">{{ item.feedback_rate_widget.rates_votes }} <BaseCmsLabel code='review' /></div>
										</div>
										<button class="cd-comments-form-button" :class="{'active': commentsForm}" @click="commentsForm = !commentsForm, itemData()">
											<span v-if="!commentsForm" class="show"><BaseCmsLabel code='comments_show_form' /></span>
											<span v-else class="hide"><BaseCmsLabel code='comments_hide_form' /></span>
										</button>
									</div>
									<div v-if="rate > 0" class="cd-comments-info-col2 cd-chart-items">
										<div class="cd-chart-item" v-for="item in ratesChart" :key="item.rate">
											<div class="cd-chart-rate">{{ item.rate }}</div>
											<div class="cd-chart-bar"><span class="cd-chart-progress-bar" :style="{width: calculateWidth(item.counter)}"></span></div>
											<div class="cd-chart-qty">{{ item.counter }}</div>
										</div>
									</div>
								</BaseFeedbackRatesWidget>
							</div>
							<!-- FIXME INTEG nedostaje dizajn
							<div class="comments-form active" v-show="commentsForm">
								<BaseFeedbackCommentsForm class="form-comment form-animated-label" v-slot="{fields, onReset, status}">
									<template v-if="!status?.success">
										<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
											<p class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel, 'hidden': item.type == 'hidden'}]">
												<BaseFormInput />
												<BaseCmsLabel tag="label" :for="item.name" :code="item.name == 'message' ?  'form_comments_message' : item.name" />
												<span class="error" v-show="errorMessage" v-html="errorMessage" />
											</p>
										</BaseFormField>
										<div class="comment-button-container">
											<button class="btn btn-send-comment" type="submit">{{ labels.get('send_comment') }}</button>
											<div class="comment-form-note">{{ labels.get('comment_form_note') }}</div>
										</div>
									</template>

									<div class="comment-success" v-if="status?.success">
										<div v-html="labels.get('comment_success')" />
										<a href="javascript:void(0);" @click="onReset">{{ labels.get('comment_add_new') }}</a>
									</div>
								</BaseFeedbackCommentsForm>
							</div>
							-->
							<BaseFeedbackComments :items="item.feedback_comment_widget.items" v-slot="{items: comments, loadMore, nextPage}">
								<div v-if="comments.length" class="cd-comments">
									<div class="cd-comment-items">
										<FeedbackCommentEntry v-for="comment in comments" :key="comment.id" :item="comment" />
									</div>
									<div class="cd-btn-container">
										<button v-if="nextPage" class="btn btn-outline cd-comments-more" @click="loadMore"><BaseCmsLabel code='comment_load_more' /></button>
									</div>
								</div>
							</BaseFeedbackComments>
						</ClientOnly>
					</div>
				</div>
			</div>

			<div class="cd-col2">
				<div class="cd-container">
					<div v-if="item.manufacturer_title" class="cd-brand">
						<NuxtLink :to="item.manufacturer_url_without_domain">{{ item.manufacturer_title }}</NuxtLink>
					</div>
					<h1 class="cd-title">{{ item.seo_h1 }}</h1>

					<!-- ratings/comments/code -->
					<div class="cd-info">
						<BaseFeedbackRatesWidget v-if="item?.feedback_rate_widget && item?.feedback_rate_widget?.rates_votes > 0" :data="item.feedback_rate_widget" v-slot="{stars}">
							<div class="cd-rate-section">
								<div v-if="mobileBreakpoint" class="cd-rate-average">{{ Number(item.feedback_rate_widget.rates).toFixed(1) }}</div>
								<span class="cd-rate" v-html="stars" />
								<div v-if="!mobileBreakpoint" class="cd-rate-average">({{ Number(item.feedback_rate_widget.rates).toFixed(1) }})</div>
								<div class="cd-rate-votes" @click="scrollToComments">{{ item.feedback_rate_widget.rates_votes }} <template v-if="!mobileBreakpoint"><BaseCmsLabel code='review' /></template></div>
							</div>
						</BaseFeedbackRatesWidget>
						<div class="cd-code"><BaseCmsLabel code='sku' />:{{ item.code }}</div>
					</div>

					<CatalogProductBadges :item="item" :types="['discount', 'uau', 'condition', 'text']" class="cd-badges" />
					<CatalogProductPrice :item="item" class="cd-price" />
					<div v-if="installmentPrice" class="cd-price-installment">
						<strong>
							<BaseCmsLabel code="installments_price_text" :replace="[{'%PRICE%': formatCurrency(installmentPrice)}]" :strip-html="true" />
						</strong>
						<span @click="modal.open('flyout', {mode: 'installments', header: true, content: {installment_calculation: item.installments_calculation, installment_list: item.installments_list_data}})"><BaseCmsLabel code="read_more" /></span>
					</div>

					<CatalogDynamicPrice :item="item" />
					
					<CatalogProductServices mode="badges" :item="item" />
					<CatalogProductServices :item="item" />

					<CatalogProductStatus :item="item" />

					<template v-if="!item.temporary_unavailable">
						<template v-if="item.is_available">
							<div class="cd-add-to-cart">
								<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :disabled="item.xxx && !isAdult" v-if="item.is_available" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1, services: selectedServices}">
									<div v-if="loading" class="btn cd-btn-add btn-loader"><div></div><div></div><div></div></div>
									<div v-else class="btn cd-btn-add" @click="onAddToCart()"><BaseCmsLabel code='cd_add_to_shopping_cart' tag="span" /></div>
								</BaseWebshopAddToCart>
							</div>
						</template>
						<template v-else-if="item.feedback_notification_widget">
							<div class="cd-add-to-cart">
								<!-- FIXME INTEG dizajn?
								<FeedbackNotificationForm :itemStatus="item.status" />
								-->
							</div>
						</template>
					</template>
				</div>

				<!-- shipping info -->
				<div v-if="item.shipping_options && item.active_shipping_options_count" class="cd-item-info-shipping cd-container flyout" @click="flyoutOpen('shipping')">
					<div class="cd-container-title icon shipping"><BaseCmsLabel code="item_delivery_tab_title" /></div>
					<div class="cd-container-content">
						<template v-for="(options, index) in item.shipping_options" :key="options.id">
							<div v-if="options.id != 'p'" class="cd-shipping-desc">
								<template v-if="item.status == 5">
									<span v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery')"></span>
									<span v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery')"></span>
									<span v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery')"></span>
									<span v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery')"></span>
									<span v-else-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium_title')"></span>&nbsp;<span v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
								</template>
								<template v-else-if="item.status != 5">
									<span v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery')"></span>
									<span v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery')"></span>
									<span v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery')"></span>
									<span v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery')"></span>
									<span v-else-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium').replace('%s%', options.fast_shipping_titles?.join(''))"></span> <template v-if="options.id != 'bb_fast'">
										<span v-if="new Date(options.min_delivery_date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', shippingDate[index])"></span>
										<span v-else-if="new Date(options.min_delivery_date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', shippingDate[index])"></span>
										<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', shippingDate[index])"></span>
									</template>
								</template>
							</div>
						</template>
					</div>
				</div>

				<div v-if="item.status != 7 && item.shipping_options?.some(obj => obj.id === 'p')" class="cd-item-info-shipping cd-container flyout" @click="flyoutOpen('stores')">
					<div class="cd-container-title icon stores">
						<template v-if="item.status == '4' && item.warehouses_display"><BaseCmsLabel code="item_delivery_tab_title" /></template>
						<template v-else><BaseCmsLabel code="item_stores_tab_title" /> <template v-if="item.warehouses_display?.length">({{ item.warehouses_display?.filter(warehouse => warehouse.available_qty > 0).length }})</template></template>
					</div>
					<div class="cd-container-content">
						<template v-if="item.status == 5">
							<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_time_of_delivery_preorder_single').replace('%s%', item.warehouses_single_pickup_display.title).replace('%s2%', formatDate(item.shipping_date))"></span>
							<span v-else v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
						</template>
						<template v-else-if="item.shipping_options?.some(obj => obj.id === 'p' && obj.active == true)">
							<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_delivery_pickup_single').replace('%s%', item.warehouses_single_pickup_display.title)"></span>
							<span v-else v-html="labels.get('item_delivery_pickup_store')"></span><span v-if="new Date(date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', pickupDate)"></span>
							<span v-else-if="new Date(date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', pickupDate)"></span>
							<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', pickupDate)"></span>
						</template>
					</div>
				</div>

				<!-- sellers -->
				<div v-if="item.seller_id && firstOtherOffer && (!firstMainOffer || item.offers_other_total != 1)" class="cd-seller-item cd-container flyout" @click="flyoutOpen('seller', {'offers': item.offers, 'id': item.offer_id})">
					<div class="cd-container-title icon seller"><BaseCmsLabel code="tab_seller_items" /></div>
					<div class="cd-container-content">
						<div class="cd-seller-desc cd-flyout-btn" v-html="offersDescl"></div>
					</div>
				</div>

				<!-- energy box -->
				<div v-if="energyAttr" class="cd-energy-item cd-container flyout" @click="flyoutOpen('energy', item?.energy_image_upload_path_thumb_energy_gallery)">
					<div class="cd-container-title icon energy"><BaseCmsLabel code="energy_title" /></div>
					<div class="cd-container-content cd-energy-content">
						<BaseUiImage v-if="energyAttr.image_upload_path" :src="energyAttr.image_upload_path" width="40" height="22" default="/images/no-image-50.jpg" :alt="energyAttr.title" />
						<BaseCmsLabel code='energy_info' />
					</div>
				</div>
			</div>
			<CatalogAgeVerificationOverlay :item="item" class="cp-detail-age-verification" />
		</div>
	</BaseCatalogDetail>
	<ClientOnly>
		<Teleport to="body">
			<CatalogCompare />
		</Teleport>
	</ClientOnly>	
</template>

<script setup>
	const webshop = useWebshop();
	const config = useAppConfig();
	const endpoints = useEndpoints();
	const route = useRoute();
	const labels = useLabels();
	const {emit} = useEventBus();
	const {generateThumbs} = useImages();
	const {formatCurrency} = useCurrency();
	const {getLastUrlSegment} = useUrl();
	const modal = useModal();
	const item = useState('product');
	const {openAgeModal, isAdult} = useAgeVerification();
	const {isLoyalty, installmentPrice} = useProductData(item.value);

	/*
	FIXME:
	- xxx age verification
	*/

	//rwd
	const {mobileBreakpoint} = inject('rwd');

	//remove script
	/*
	function removeScript() {
		// Find the script by id and remove it
		const script = document.getElementById('4thvision');
		if (script) {
			script.remove();
		}
	}
	*/

	// generate thumbnails
	async function imageThumbs() {
		return Promise.all([
			generateThumbs({
				data: item.value,
				preset: 'catalogDetail'
			}),
			generateThumbs({
				data: item.value,
				preset: 'catalogEnergy'
			}),
		]);
	}

	/* 
		Filter out unwanted attributes. Group same attribute values into one attribute. Data structure:
		{
			"Boja": {
				"values": ["Crvena", "Zelena"],
				"unit": ""
			}
		}
	*/
	const itemsAttributes = computed(() => {
		if (!item?.value?.attributes) return null;

		return item.value.attributes.reduce((acc, attr) => {
			if (['attribute_badges', 'superbenefits', 'katalog-9001'].includes(attr.code)) return acc;

			const key = attr.attribute_title;
			if (!acc[key]) {
				acc[key] = { values: [], unit: attr.attribute_unit || '' };
			}
			acc[key].values.push(attr.title);
			return acc;
		}, {});
	});

	// Toggle between showing all attributes and only the first 6
	const showAttr = ref(false);
	const displayedAttributes = computed(() => {
		const attributesArray = Object.entries(itemsAttributes.value || {});
		if(!attributesArray.length) return null;

		if (showAttr.value) {
			return attributesArray.map(([title, value]) => ({ title, ...value }));
		} else {
			return attributesArray.slice(0, 6).map(([title, value]) => ({ title, ...value }));
		}
	});

	//manufacturer content
	const toggleSyndicate = ref(false);

	//comments
	let toggleCommentEl = ref(false);
	function toggleCommentContainer() {
		toggleCommentEl.value = !toggleCommentEl.value;
	}

	// rates chart
	const ratesChart = reactive([{ rate: 1, counter: 0 },{ rate: 2, counter: 0 },{ rate: 3, counter: 0 },{ rate: 4, counter: 0 },{ rate: 5, counter: 0 },]);
	const ratingsData = item?.value?.feedback_comment_widget.items;
	Object.values(ratingsData).forEach((rating) => {
		const rateIndex = Number(rating.rate) - 1;
		ratesChart[rateIndex].counter++;
	});
	const totalVotes = computed(() => Object.keys(ratingsData).length);
	const calculateWidth = (count) => {
		return totalVotes.value ? `${((count / totalVotes.value) * 100).toFixed(0)}%` : '0%';
	};
	
	//scroll to comments
	const comments = ref();
	function scrollToComments() {
		toggleCommentEl.value = true;

		if(comments.value) {
			const element = comments.value;
			const offset = -80;

			const elementPosition = element.getBoundingClientRect().top + window.scrollY;
			const offsetPosition = elementPosition + offset;

			window.scrollTo({top: offsetPosition,behavior: 'smooth'});
		}
	}

	// show/hide comments form
	const commentsForm = ref(0);

	//shipping info
	//FIXME INTEG jezik
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	const calendarMonth = ['januarja', 'februarja', 'marca', 'aprila', 'maja', 'junija', 'julija', 'avgusta', 'septembra', 'oktobra', 'novembra', 'decembra'];
	const calendarDays = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote'];

	const shippingDate = computed(() => {
		return item?.value?.shipping_options?.map((item) => {
			const shippingDateDay = new Date(item.min_delivery_date * 1000).getDay();
			const shippingDateMonth = new Date(item.min_delivery_date * 1000).getMonth();

			if (calendarDays[shippingDateDay] && calendarMonth[shippingDateMonth]) {
				if (new Date(item.min_delivery_date * 1000).toDateString() === new Date().toDateString() || new Date(item.min_delivery_date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
					return new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				} else {
					return calendarDays[shippingDateDay] + ', ' + new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				}
			} else {
				return new Date(item.min_delivery_date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
			}
		});
	});

	const date = (item.value.status == 5) ? item.value.shipping_date : item.value.shipping_options?.find(option => option.id === 'p')?.min_delivery_date || null;
	const pickupDate = computed(() => {
		const pickupDateDay = new Date(date * 1000).getDay();
		const pickupDateMonth = new Date(date * 1000).getMonth();

		if (calendarDays[pickupDateDay] && calendarMonth[pickupDateMonth]) {
			if (new Date(date * 1000).toDateString() === new Date().toDateString() || new Date(date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
				return new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			} else {
				return calendarDays[pickupDateDay] + ', ' + new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			}
		} else {
			return new Date(date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
		}
	});

	//sellers
	const firstOtherOffer = computed(() => {
		return item?.value?.first_other_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_other_offer_id.toString()) : null;
	});
	const firstMainOffer = computed(() => {
		return item?.value?.first_main_seller_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_main_seller_offer_id.toString()) : null;
	});
	const offersFlyoutTitle = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_flyout_title')
			.replace('%s%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text);
	});
	const offersDescl = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_others')
			.replace('%offers_total%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text)
			.replace('%price%', firstOtherOffer.value.price_custom ?? 0);
	});

	//energy
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'];
		return item?.value?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//flyout open
	function flyoutOpen(mode, value) {
		//energy
		if(mode == 'energy') {
			emit('flyoutUpdate', {'flyout_mode': 'energy', 'flyout_title': labels.get('energy_flyout_title'), 'flyout_content': value ? value : null});
		}

		//shipping
		if(mode == 'shipping') {
			const content = {
				shipping_dates: shippingDate,
				date: item.value.shipping_date,
				status: item.value.status,
				shipping_options: item.value.shipping_options
			};

			emit('flyoutUpdate', {'flyout_mode': 'shipping', 'flyout_title': labels.get('shipping'), 'flyout_content': content ? content : null});
		}

		//stores
		//FIXME INTEG dokumentaicija
		if(mode == 'stores') {
			let title = labels.get('item_stores_flyout_title');
			if(item?.value?.status == 4 && item?.value?.warehouses_display) {
				title = labels.get('item_stores_flyout_title')
			}

			const content = {
				status: item.value.status,
				warehouses_single_pickup_display: item.value.warehouses_single_pickup_display,
				last_piece_sale: item.value.last_piece_sale,
				warehouses_display: item.value.warehouses_display,
				is_available: item.value.is_available,
				shipping_options: item.value.shipping_options
			};

			emit('flyoutUpdate', {'flyout_mode': 'stores', 'flyout_title': title, 'flyout_content': content ? content : null});
		}

		//seller
		if(mode == 'seller') {
			emit('flyoutUpdate', {'flyout_mode': 'sellers', 'flyout_title': offersFlyoutTitle, 'flyout_content': value && value.offers ? value.offers : null, 'flyout_offer_id': value && value.id ? value.id : null});
		}
	}
</script>

<style lang="less" scoped>
	//tabs
	:deep(.accordion-panel){
		background: var(--white); border-radius: 12px; margin-bottom: 24px; 
		&.active{
			.accordion-panel-header:after{.rotate(180deg);}
			.accordion-panel-body{padding-bottom: 24px;}
		}
	}
	:deep(.accordion-panel-header){
		display: flex; align-items: center; font-size: 24px; line-height: 1.4; font-weight: 600; color: var(--black); position: relative; cursor: pointer; padding: 17px 24px;
		&:after{.icon-arrow-down(); font: 12px/1 var(--fonti); font-weight: normal; color: var(--black); .transition(transform); margin-left: auto;}
		&.no-icon{
			cursor: default;
			&:before{content: none;}
		}

		@media (max-width: @m){
			height: 55px; font-size: 18px;
			&:before{font-size: 10px;}
			&.active:before{.rotate(180deg);}
		}
	}
	:deep(.accordion-panel-body){padding: 0 24px;}
	.cd-tab-more{
		display: inline-flex; font-size: 12px; text-decoration: underline; cursor: pointer; margin-top: 15px;
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}

	//main
	.cd-bc{
		padding-bottom: 32px;
		
		@media (max-width: @m){display: none;}
	}
	.cd-main{
		display: flex; margin-bottom: 58px; position: relative; gap: var(--elementGap);

		@media (max-width: @m){flex-direction: column-reverse; margin: 0 0 12px;}
	}
	.cd-container{
		margin-bottom: 24px; padding: 18px 16px; background: var(--white); border-radius: 12px; position: relative;
		&.flyout{
			padding-right: 50px; cursor: pointer;
			&:before{.icon-arrow-down(); font: 12px/1 var(--fonti); color: var(--black); position: absolute; right: 18px; top: 50%; transform: translateY(-50%) rotate(-90deg);}
		}

		@media (max-width: @m){
			margin-bottom: 12px; padding: 18px 12px; border-radius: 0;
			&.flyout:before{right: 12px;}
		}
	}
	.cd-container-title{
		display: flex; align-items: center; font-size: 18px; font-weight: 600; position: relative;
		&.icon{
			padding-left: 30px;
			&:before{font: 15px/1 var(--fonti); color: var(--black); position: absolute; left: 0;}
		}
		&.shipping:before{.icon-shipping(); font-size: 17px;}
		&.stores:before{.icon-pin(); font-size: 19px; left: 3px;}
		&.seller:before{.icon-store(); font-size: 19px; left: 2px;}
		&.energy:before{.icon-energy(); font-size: 19px; left: 2px;}

		@media (max-width: @m){
			font-size: 16px;
			&.icon{
				padding-left: 28px;
				&:before{font-size: 14px;}
			}
			&.shipping:before{font-size: 15px;}
			&.stores:before{font-size: 17px; left: 2px;}
			&.seller:before{font-size: 17px; left: 1px;}
			&.energy:before{font-size: 17px; left: 1px;}
		}
	}
	.cd-container-content{
		margin-top: 16px; font-size: 15px;

		@media (max-width: @m){margin-top: 12px; font-size: 12px; line-height: 1.3;}
	}
	.cd-col1{
		width: 952px; flex-shrink: 0;

		@media (max-width: @l){width: 700px;}
		@media (max-width: @t){width: 550px;}
		@media (max-width: @m){width: 100%;}
	}

	//attribute
	.cd-tab-attributes{
		display: grid; grid-template-columns: 1fr 1fr; gap: var(--elementGap); font-size: 14px; line-height: 1.2; position: relative;
		&:before{.pseudo(1px,auto); background: var(--gray2); position: absolute; left: 50%; top: 0; bottom: 12px; z-index: 1;}

		@media (max-width: @m){
			display: block; gap: 14px; font-size: 12px;
			&:before{content: none;}
		}
	}
	.cd-tab-attribute{
		display: flex;

		@media (max-width: @m){
			margin-bottom: 14px;
			&:last-child{margin-bottom: 0;}
		}
	}
	.cd-tab-attribute-title{width: 50%; flex-shrink: 0; padding-right: 30px; color: #6D6D6D;}
	.cd-tab-attribute-value{width: 50%;}

	//description
	.cd-tab-content-desc{
		font-size: 14px; line-height: 1.5;
		:deep(ul), :deep(ol){margin: 0 0 24px 20px;}
		:deep(h2), :deep(h3), :deep(h4), :deep(h5){padding: 15px 0 10px; font-weight: 600;}
		:deep(iframe), :deep(video){width: 100%;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}
		:deep(p:last-child){padding-bottom: 0;}
	}
	.cd-tab-more-desc{margin-top: 24px;}
	.cd-tab-syndicate{
		max-height: 500px; overflow: hidden;
		&.active{max-height: none;}
	}

	//comments/rates
	.cd-comments-info{display: flex; align-items: center;}
	.cd-comments-info-col1{flex-grow: 1; padding-right: 30px;}
	.cd-comments-rate-average{margin-bottom: 12px; font-size: 28px; font-weight: 600; line-height: 1.2; letter-spacing: -0.28px;}
	.cd-chart-items{display: flex; flex-direction: column-reverse; width: 372px; flex-shrink: 0;}
	.cd-chart-item{display: flex; align-items: center; width: 100%; gap: 6px; font-size: 11px; color: #6D6D6D;}
	.cd-chart-rate{display: flex; align-items: center; flex-shrink: 0; width: 60px; margin-right: 6px; position: relative;}
	.cd-chart-bar{display: flex; align-items: center; flex-grow: 1; height: 4px; background: var(--gray2); border-radius: 22px; position: relative;}
	.cd-chart-progress-bar{height: 4px; background: var(--blueDark); border-radius: 22px; position: absolute; top: 0; bottom: 0; left: 0; z-index: 1;}
	.cd-chart-qty{width: 10px; margin-left: 6px; text-align: center;}
	.cd-comments-form-button{margin-top: 12px; padding: 0 24px;}
	.cd-comments{margin-top: 28px;}
	.cd-comment-items{column-count: 2; column-gap: 12px;}
	.cd-btn-container{display: flex; justify-content: center; margin-top: 16px;}

	//main info
	.cd-col2{flex-grow: 1;}
	.cd-brand{
		display: flex; margin-bottom: 6px; font-size: 14px;
		a{text-decoration: underline; color: var(--black);}
		@media (min-width: @t){
			a:hover{text-decoration: none;}
		}
	}
	.cd-title{
		display: block; padding: 0 0 12px; font-size: 20px; line-height: 1.3;
	}

	.cd-info{display: flex; align-items: center; padding-bottom: 10px;}
	.cd-rate-section{display: flex; align-items: flex-end; flex-grow: 1; padding-right: 20px; font-size: 11px; font-weight: 300; color: var(--black);}
	.cd-rate{
		display: flex; align-items: center;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 18px; height: 18px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(18px,18px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.icon-star){
			width: 19px; height: 19px;
			&:after{width: 19px; height: 19px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}

		@media (max-width: @m){
			:deep(.icon-star-empty){
				width: 15px; height: 15px; margin-right: 1px;
				&:after{width: 15px; height: 15px;}
				
			}
			:deep(.icon-star){
				width: 16px; height: 16px;
				&:after{width: 16px; height: 16px;}
			}
		}
	}
	.cd-rate-average{
		margin-left: 3px;

		@media (max-width: @m){margin: 0 4px 0 0; font-size: 12px; line-height: 1.2; font-weight: 600;}
	}
	.cd-rate-votes{
		margin-left: 5px; text-decoration: underline; cursor: pointer;
		&:hover{text-decoration: none;}

		@media (max-width: @m){margin-left: 4px;}
	}
	.cd-code{display: block; font-size: 12px; color: var(--gray5);}

	//price
	.cd-price{
		display: flex; align-items: center; padding-top: 25px; padding-bottom: 7px; gap: 10px; row-gap: 7px;
		.red{color: var(--errorColor);}

		@media (max-width: @m){align-items: baseline}
	}
	:deep(.current-price){
		font-size: 28px; font-weight: 700;
		:deep(.p-comma){display: none;}
		:deep(.p-d){font-size: 16px; vertical-align: text-top;}
	}
	:deep(.old-price){
		font-size: 14px; padding: 0;
	}
	.cd-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 15px; height: 15px; margin-left: 8px; position: relative;
		&:before{.icon-info(); font: 15px/1 var(--fonti); color: var(--black);}
		&:hover .cd-price-tooltip{display: flex;}
	}
	.cd-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: var(--black); position: absolute; right: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); right: 12px; z-index: 1; .rotate(45deg);}
	}
	.cd-price-installment{
		display: block; font-size: 14px; padding-bottom: 5px; font-weight: bold; display: flex; gap: 7px;
		:deep(p){padding: 0;}
		span{
			font-weight: normal; text-decoration: underline; cursor: pointer;
			&:hover{text-decoration: none;}
		}
	}

	//dynamic price
	.cd-conf-price-label{
		display: none; margin-top: 8px; font-size: 14px; color: var(--black);
		&.active{display: block;}
		:deep(a){
			color: var(--black);
			&:hover{text-decoration: none;}
		}
	}
	.cd-conf-price-section{width: 100%; margin-top: 12px;}
	.cd-conf-price-header{display: flex; align-items: center; margin-bottom: 6px;}
	.cd-conf-price-title{flex-grow: 1; font-size: 14px; color: var(--errorColor);}
	.cd-conf-price-remove{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 20px; height: 20px; background: var(--gray3); border-radius: 100%; font-size: 0; line-height: 0; text-decoration: none; position: relative; .transition(background); cursor: pointer;
		&:before{.icon-x(); font: 10px/1 var(--fonti); color: var(--black); font-weight: 600; text-indent: 1px;}
	}
	.cd-conf-price{
		display: flex; align-items: center; flex-grow: 1;
		span{display: block;}
		.cd-current-price{flex-grow: 1; flex-shrink: 0; color: var(--errorColor);}
	}
	.cd-conf-price-timer{display: flex; align-items: center; flex-shrink: 0; font-size: 15px; color: var(--gray5); position: relative;}
	.cd-conf-price-timer-bottom{
		display: flex; align-items: center;
		.countdown{
			display: flex; flex-shrink: 0;
			:deep(&>span){display: flex; justify-content: center; min-width: 36px; margin-left: 12px; position: relative;}
		}
		:deep(.value){font-size: 28px; font-weight: 600; color: var(--errorColor); position: relative;}
		:deep(.frame){font-size: 8px; font-weight: 400; text-align: center; color: var(--gray5); position: absolute; bottom: 0; top: 100%;}
	}
	.cd-conf-price-note{display: block; width: 100%; margin-top: 22px; font-size: 10px; color: var(--gray5); position: relative;}

	//add to cart
	.cd-add-to-cart{display: flex; justify-content: center; margin-top: 24px;}
	.cd-btn-add{
		width: 100%; height: 60px; border-radius: 30px; font-size: 18px; cursor: pointer;
		span{
			padding-left: 32px; position: relative;
			&:before{.icon-cart(); font: 21px/1 var(--fonti); color: var(--white); position: absolute; left: 0;}
		}

		@media (max-width: @m){height: 52px; border-radius: 26px; font-size: 15px;}
	}

	//shipping
	.cd-shipping-desc{
		display: block; margin-bottom: 16px; font-size: 15px; line-height: 1.35;
		&:last-child{margin-bottom: 0;}

		@media (max-width: @m){margin-bottom: 3px; font-size: 12px; line-height: 1.3;}
	}

	//energy
	.cd-energy-content{
		display: flex; align-items: center;
		:deep(img){display: block; margin-right: 12px; width: auto; height: auto; max-width: 40px; max-height: 22px;}
	}

	.age-verification{
		.cd-container, .cd-tab{
			*, &:before, &:after{filter: blur(4px);}
		}
	}
</style>